# Generated by Django 4.2.5 on 2023-09-06 13:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import mdeditor.fields


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('blog', '0004_rename_analyticscode_blogsettings_analytics_code_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='article',
            options={'get_latest_by': 'id', 'ordering': ['-article_order', '-pub_time'], 'verbose_name': 'article', 'verbose_name_plural': 'article'},
        ),
        migrations.AlterModelOptions(
            name='category',
            options={'ordering': ['-index'], 'verbose_name': 'category', 'verbose_name_plural': 'category'},
        ),
        migrations.AlterModelOptions(
            name='links',
            options={'ordering': ['sequence'], 'verbose_name': 'link', 'verbose_name_plural': 'link'},
        ),
        migrations.AlterModelOptions(
            name='sidebar',
            options={'ordering': ['sequence'], 'verbose_name': 'sidebar', 'verbose_name_plural': 'sidebar'},
        ),
        migrations.AlterModelOptions(
            name='tag',
            options={'ordering': ['name'], 'verbose_name': 'tag', 'verbose_name_plural': 'tag'},
        ),
        migrations.RemoveField(
            model_name='article',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='article',
            name='last_mod_time',
        ),
        migrations.RemoveField(
            model_name='category',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='category',
            name='last_mod_time',
        ),
        migrations.RemoveField(
            model_name='links',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='sidebar',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='tag',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='tag',
            name='last_mod_time',
        ),
        migrations.AddField(
            model_name='article',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='article',
            name='last_modify_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='modify time'),
        ),
        migrations.AddField(
            model_name='category',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='category',
            name='last_modify_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='modify time'),
        ),
        migrations.AddField(
            model_name='links',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='sidebar',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='tag',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='tag',
            name='last_modify_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='modify time'),
        ),
        migrations.AlterField(
            model_name='article',
            name='article_order',
            field=models.IntegerField(default=0, verbose_name='order'),
        ),
        migrations.AlterField(
            model_name='article',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='author'),
        ),
        migrations.AlterField(
            model_name='article',
            name='body',
            field=mdeditor.fields.MDTextField(verbose_name='body'),
        ),
        migrations.AlterField(
            model_name='article',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blog.category', verbose_name='category'),
        ),
        migrations.AlterField(
            model_name='article',
            name='comment_status',
            field=models.CharField(choices=[('o', 'Open'), ('c', 'Close')], default='o', max_length=1, verbose_name='comment status'),
        ),
        migrations.AlterField(
            model_name='article',
            name='pub_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='publish time'),
        ),
        migrations.AlterField(
            model_name='article',
            name='show_toc',
            field=models.BooleanField(default=False, verbose_name='show toc'),
        ),
        migrations.AlterField(
            model_name='article',
            name='status',
            field=models.CharField(choices=[('d', 'Draft'), ('p', 'Published')], default='p', max_length=1, verbose_name='status'),
        ),
        migrations.AlterField(
            model_name='article',
            name='tags',
            field=models.ManyToManyField(blank=True, to='blog.tag', verbose_name='tag'),
        ),
        migrations.AlterField(
            model_name='article',
            name='title',
            field=models.CharField(max_length=200, unique=True, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='article',
            name='type',
            field=models.CharField(choices=[('a', 'Article'), ('p', 'Page')], default='a', max_length=1, verbose_name='type'),
        ),
        migrations.AlterField(
            model_name='article',
            name='views',
            field=models.PositiveIntegerField(default=0, verbose_name='views'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='article_comment_count',
            field=models.IntegerField(default=5, verbose_name='article comment count'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='article_sub_length',
            field=models.IntegerField(default=300, verbose_name='article sub length'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='google_adsense_codes',
            field=models.TextField(blank=True, default='', max_length=2000, null=True, verbose_name='adsense code'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='open_site_comment',
            field=models.BooleanField(default=True, verbose_name='open site comment'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='show_google_adsense',
            field=models.BooleanField(default=False, verbose_name='show adsense'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='sidebar_article_count',
            field=models.IntegerField(default=10, verbose_name='sidebar article count'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='sidebar_comment_count',
            field=models.IntegerField(default=5, verbose_name='sidebar comment count'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='site_description',
            field=models.TextField(default='', max_length=1000, verbose_name='site description'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='site_keywords',
            field=models.TextField(default='', max_length=1000, verbose_name='site keywords'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='site_name',
            field=models.CharField(default='', max_length=200, verbose_name='site name'),
        ),
        migrations.AlterField(
            model_name='blogsettings',
            name='site_seo_description',
            field=models.TextField(default='', max_length=1000, verbose_name='site seo description'),
        ),
        migrations.AlterField(
            model_name='category',
            name='index',
            field=models.IntegerField(default=0, verbose_name='index'),
        ),
        migrations.AlterField(
            model_name='category',
            name='name',
            field=models.CharField(max_length=30, unique=True, verbose_name='category name'),
        ),
        migrations.AlterField(
            model_name='category',
            name='parent_category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='blog.category', verbose_name='parent category'),
        ),
        migrations.AlterField(
            model_name='links',
            name='is_enable',
            field=models.BooleanField(default=True, verbose_name='is show'),
        ),
        migrations.AlterField(
            model_name='links',
            name='last_mod_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='modify time'),
        ),
        migrations.AlterField(
            model_name='links',
            name='link',
            field=models.URLField(verbose_name='link'),
        ),
        migrations.AlterField(
            model_name='links',
            name='name',
            field=models.CharField(max_length=30, unique=True, verbose_name='link name'),
        ),
        migrations.AlterField(
            model_name='links',
            name='sequence',
            field=models.IntegerField(unique=True, verbose_name='order'),
        ),
        migrations.AlterField(
            model_name='links',
            name='show_type',
            field=models.CharField(choices=[('i', 'index'), ('l', 'list'), ('p', 'post'), ('a', 'all'), ('s', 'slide')], default='i', max_length=1, verbose_name='show type'),
        ),
        migrations.AlterField(
            model_name='sidebar',
            name='content',
            field=models.TextField(verbose_name='content'),
        ),
        migrations.AlterField(
            model_name='sidebar',
            name='is_enable',
            field=models.BooleanField(default=True, verbose_name='is enable'),
        ),
        migrations.AlterField(
            model_name='sidebar',
            name='last_mod_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='modify time'),
        ),
        migrations.AlterField(
            model_name='sidebar',
            name='name',
            field=models.CharField(max_length=100, verbose_name='title'),
        ),
        migrations.AlterField(
            model_name='sidebar',
            name='sequence',
            field=models.IntegerField(unique=True, verbose_name='order'),
        ),
        migrations.AlterField(
            model_name='tag',
            name='name',
            field=models.CharField(max_length=30, unique=True, verbose_name='tag name'),
        ),
    ]
