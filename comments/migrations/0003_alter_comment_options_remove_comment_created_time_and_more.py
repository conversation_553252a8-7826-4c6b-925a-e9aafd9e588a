# Generated by Django 4.2.5 on 2023-09-06 13:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('blog', '0005_alter_article_options_alter_category_options_and_more'),
        ('comments', '0002_alter_comment_is_enable'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='comment',
            options={'get_latest_by': 'id', 'ordering': ['-id'], 'verbose_name': 'comment', 'verbose_name_plural': 'comment'},
        ),
        migrations.RemoveField(
            model_name='comment',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='comment',
            name='last_mod_time',
        ),
        migrations.AddField(
            model_name='comment',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='comment',
            name='last_modify_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='last modify time'),
        ),
        migrations.AlterField(
            model_name='comment',
            name='article',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blog.article', verbose_name='article'),
        ),
        migrations.AlterField(
            model_name='comment',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='author'),
        ),
        migrations.AlterField(
            model_name='comment',
            name='is_enable',
            field=models.BooleanField(default=False, verbose_name='enable'),
        ),
        migrations.AlterField(
            model_name='comment',
            name='parent_comment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='comments.comment', verbose_name='parent comment'),
        ),
    ]
