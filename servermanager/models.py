from django.db import models


# Create your models here.
class commands(models.Model):
    title = models.<PERSON><PERSON><PERSON><PERSON>('命令标题', max_length=300)
    command = models.Cha<PERSON><PERSON><PERSON>('命令', max_length=2000)
    describe = models.Cha<PERSON><PERSON><PERSON>('命令描述', max_length=300)
    creation_time = models.DateTimeField('创建时间', auto_now_add=True)
    last_modify_time = models.DateTimeField('修改时间', auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = '命令'
        verbose_name_plural = verbose_name


class EmailSendLog(models.Model):
    emailto = models.Char<PERSON>ield('收件人', max_length=300)
    title = models.CharField('邮件标题', max_length=2000)
    content = models.TextField('邮件内容')
    send_result = models.BooleanField('结果', default=False)
    creation_time = models.DateTimeField('创建时间', auto_now_add=True)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = '邮件发送log'
        verbose_name_plural = verbose_name
        ordering = ['-creation_time']
