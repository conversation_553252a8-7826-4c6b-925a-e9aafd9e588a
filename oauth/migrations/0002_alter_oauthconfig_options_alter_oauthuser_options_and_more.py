# Generated by Django 4.2.5 on 2023-09-06 13:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('oauth', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='oauthconfig',
            options={'ordering': ['-creation_time'], 'verbose_name': 'oauth配置', 'verbose_name_plural': 'oauth配置'},
        ),
        migrations.AlterModelOptions(
            name='oauthuser',
            options={'ordering': ['-creation_time'], 'verbose_name': 'oauth user', 'verbose_name_plural': 'oauth user'},
        ),
        migrations.RemoveField(
            model_name='oauthconfig',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='oauthconfig',
            name='last_mod_time',
        ),
        migrations.RemoveField(
            model_name='oauthuser',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='oauthuser',
            name='last_mod_time',
        ),
        migrations.AddField(
            model_name='oauthconfig',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='oauthconfig',
            name='last_modify_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='last modify time'),
        ),
        migrations.AddField(
            model_name='oauthuser',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='oauthuser',
            name='last_modify_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='last modify time'),
        ),
        migrations.AlterField(
            model_name='oauthconfig',
            name='callback_url',
            field=models.CharField(default='', max_length=200, verbose_name='callback url'),
        ),
        migrations.AlterField(
            model_name='oauthconfig',
            name='is_enable',
            field=models.BooleanField(default=True, verbose_name='is enable'),
        ),
        migrations.AlterField(
            model_name='oauthconfig',
            name='type',
            field=models.CharField(choices=[('weibo', 'weibo'), ('google', 'google'), ('github', 'GitHub'), ('facebook', 'FaceBook'), ('qq', 'QQ')], default='a', max_length=10, verbose_name='type'),
        ),
        migrations.AlterField(
            model_name='oauthuser',
            name='author',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='author'),
        ),
        migrations.AlterField(
            model_name='oauthuser',
            name='nickname',
            field=models.CharField(max_length=50, verbose_name='nickname'),
        ),
    ]
