{% load blog_tags %}
{% load i18n %}
<div id="secondary" class="widget-area" role="complementary">
    <aside id="search-2" class="widget widget_search">
        <form role="search" method="get" id="searchform" class="searchform" action="/search">
            <div>
                <label class="screen-reader-text" for="s">{% trans 'search' %}：</label>
                <input type="text" value="" name="q" id="q"/>
                <input type="submit" id="searchsubmit" />
            </div>
        </form>
    </aside>
    {% if extra_sidebars %}
        {% for sidebar in extra_sidebars %}

            <aside class="widget_text widget widget_custom_html"><p class="widget-title">
                {{ sidebar.name }}</p>
                <div class="textwidget custom-html-widget">
                    {{ sidebar.content|custom_markdown|safe }}
                </div>
            </aside>
        {% endfor %}
    {% endif %}
    {% if most_read_articles %}

        <aside id="views-4" class="widget widget_views"><p class="widget-title">Views</p>
            <ul>
                {% for a in most_read_articles %}
                    <li>
                        <a href="{{ a.get_absolute_url }}" title="{{ a.title }}">
                            {{ a.title }}
                        </a> - {{ a.views }} views
                    </li>
                {% endfor %}
            </ul>

        </aside>
    {% endif %}
    {% if sidebar_categorys %}
        <aside id="su_siloed_terms-2" class="widget widget_su_siloed_terms"><p class="widget-title">{% trans 'category' %}</p>
            <ul>
                {% for c in sidebar_categorys %}
                    <li class="cat-item cat-item-184"><a href={{ c.get_absolute_url }}>{{ c.name }}</a>
                    </li>
                {% endfor %}
            </ul>
        </aside>
    {% endif %}
    {% if sidebar_comments and  open_site_comment %}
        <aside id="ds-recent-comments-4" class="widget ds-widget-recent-comments"><p class="widget-title">{% trans 'recent comments' %}</p>

            <ul id="recentcomments">
                {% for c in sidebar_comments %}
                    <li class="recentcomments">
                <span class="comment-author-link">
                    {{ c.author.username }}</span>
                        {% trans 'published on' %}《
                        <a href="{{ c.article.get_absolute_url }}#comment-{{ c.pk }}">{{ c.article.title }}</a>》
                    </li>
                {% endfor %}
            </ul>
        </aside>
    {% endif %}
    {% if recent_articles %}
        <aside id="recent-posts-2" class="widget widget_recent_entries"><p class="widget-title">{% trans 'recent articles' %}</p>
            <ul>

                {% for a in  recent_articles %}
                    <li><a href="{{ a.get_absolute_url }}" title="{{ a.title }}">
                        {{ a.title }}
                    </a></li>
                {% endfor %}
            </ul>
        </aside>
    {% endif %}
    {% if sidabar_links %}
        <aside id="linkcat-0" class="widget widget_links"><p class="widget-title">{% trans 'bookmark' %}</p>
            <ul class='xoxo blogroll'>
                {% for l in sidabar_links %}
                    <li>
                        <a href="{{ l.link }}" target="_blank" title="{{ l.name }}">{{ l.name }}</a>
                    </li>
                {% endfor %}

            </ul>
        </aside>
    {% endif %}
    {% if show_google_adsense %}
        <aside id="text-2" class="widget widget_text"><p class="widget-title">Google AdSense</p>
            <div class="textwidget">
                {{ google_adsense_codes|safe }}
            </div>
        </aside>
    {% endif %}
    {% if sidebar_tags %}
        <aside id="tag_cloud-2" class="widget widget_tag_cloud"><p class="widget-title">{% trans 'Tag Cloud' %}</p>
            <div class="tagcloud">
                {% for tag,count,size in sidebar_tags %}
                    <a href="{{ tag.get_absolute_url }}"
                       class="tag-link-{{ tag.id }} tag-link-position-{{ tag.id }}"
                       style="font-size: {{ size }}pt;" title="{{ count }}个话题"> {{ tag.name }}
                    </a>
                {% endfor %}
            </div>
        </aside>
    {% endif %}
    <aside id="text-2" class="widget widget_text"><p class="widget-title">{% trans 'Welcome to star or fork the source code of this site' %}</p>
        <div class="textwidget">

            <p><a href="https://github.com/liangliangyy/DjangoBlog" rel="nofollow"><img
                    src="https://resource.lylinux.net/img.shields.io/github/stars/liangliangyy/djangoblog.svg?style=social&amp;label=Star"
                    alt="GitHub stars"></a> <a href="https://github.com/liangliangyy/DjangoBlog" rel="nofollow"><img
                    src="https://resource.lylinux.net/img.shields.io/github/forks/liangliangyy/djangoblog.svg?style=social&amp;label=Fork"
                    alt="GitHub forks"></a></p>
        </div>
    </aside>

    <aside id="meta-3" class="widget widget_meta"><p class="widget-title">{% trans 'Function' %}</p>
        <ul>
            <li><a href="/admin/" rel="nofollow">{% trans 'management site' %}</a></li>
            {% if user.is_authenticated %}
                <li><a href="{% url "account:logout" %}" rel="nofollow">{% trans 'logout' %}</a>
                </li>

            {% else %}
                <li><a href="{% url "account:login" %}" rel="nofollow">{% trans 'login' %}</a></li>
            {% endif %}
            {% if user.is_superuser %}
                <li><a href="{% url 'owntracks:show_dates' %}" target="_blank">{% trans 'Track record' %}</a></li>
            {% endif %}
            <li><a href="http://gitbook.lylinux.net" target="_blank" rel="nofollow">GitBook</a></li>
        </ul>
    </aside>

    <div id="rocket" class="show" title="{% trans 'Click me to return to the top' %}"></div>
</div><!-- #secondary -->
