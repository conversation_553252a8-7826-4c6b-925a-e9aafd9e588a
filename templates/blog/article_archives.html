{% extends 'share_layout/base.html' %}
{% load blog_tags %}
{% load cache %}
{% load i18n %}
{% block header %}

    <title>{% trans 'article archive' %} | {{ SITE_DESCRIPTION }}</title>

    <meta name="description" content="{{ SITE_SEO_DESCRIPTION }}"/>
    <meta name="keywords" content="{{ SITE_KEYWORDS }}"/>
    <meta property="og:type" content="blog"/>
    <meta property="og:title" content="{{ SITE_NAME }}"/>
    <meta property="og:description" content="{{ SITE_DESCRIPTION }}"/>
    <meta property="og:url" content="{{ SITE_BASE_URL }}"/>
    <meta property="og:site_name" content="{{ SITE_NAME }}"/>

{% endblock %}
{% block content %}
    <div id="primary" class="site-content">
        <div id="content" role="main">

            <header class="archive-header">

                <p class="archive-title">{% trans 'article archive' %}</p>
            </header><!-- .archive-header -->

            <div class="entry-content">

                {% regroup article_list by pub_time.year as year_post_group %}
                <ul>
                    {% for year in year_post_group %}
                        <li>{{ year.grouper }} {% trans 'year' %}
                            {% regroup year.list by pub_time.month as month_post_group %}
                            <ul>
                                {% for month in month_post_group %}
                                    <li>{{ month.grouper }} {% trans 'month' %}
                                        <ul>
                                            {% for article in month.list %}
                                                <li><a href="{{ article.get_absolute_url }}">{{ article.title }}</a>
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                {% endfor %}
                            </ul>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div><!-- #content -->
    </div><!-- #primary -->

{% endblock %}


{% block sidebar %}
    {% load_sidebar user 'i' %}
{% endblock %}


