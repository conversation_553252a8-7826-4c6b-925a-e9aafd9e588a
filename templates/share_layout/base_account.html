<!DOCTYPE html>
<html>
<head>
    {% load static %}
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <meta name="robots" content="noindex">
    <title>{{ SITE_NAME }} | {{ SITE_DESCRIPTION }}</title>
    <link href="{% static 'account/css/account.css' %}" rel="stylesheet">
    {% load compress %}
    {% compress css %}
        <!-- Bootstrap core CSS -->
        <link href="{% static 'assets/css/bootstrap.min.css' %}" rel="stylesheet">
        <link href="{% static 'blog/css/oauth_style.css' %}" rel="stylesheet">
        <!-- IE10 viewport hack for Surface/desktop Windows 8 bug -->
        <link href="{% static 'assets/css/ie10-viewport-bug-workaround.css' %}" rel="stylesheet">
        <!-- TODC Bootstrap core CSS -->
        <link href="{% static 'assets/css/todc-bootstrap.min.css' %}" rel="stylesheet">
        <!-- Custom styles for this template -->
        <link href="{% static 'assets/css/signin.css' %}" rel="stylesheet">
    {% endcompress %}
    {% compress js %}
        <script src="{% static 'assets/js/ie10-viewport-bug-workaround.js' %}"></script>
        <script src="{% static 'assets/js/ie-emulation-modes-warning.js' %}"></script>
    {% endcompress %}
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
{% block content %}
{% endblock %}

<!-- IE10 viewport hack for Surface/desktop Windows 8 bug -->

</body>
    <script type="text/javascript" src="{% static 'blog/js/jquery-3.6.0.min.js' %}"></script>
    <script src="{% static 'account/js/account.js' %}" type="text/javascript"></script>
</html>