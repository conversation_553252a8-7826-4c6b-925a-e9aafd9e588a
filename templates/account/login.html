{% extends 'share_layout/base_account.html' %}
{% load static %}
{% load i18n %}
{% block content %}
    <div class="container">

        <h2 class="form-signin-heading text-center">Sign in with your Account</h2>

        <div class="card card-signin">
            <img class="img-circle profile-img" src="{% static 'blog/img/avatar.png' %}" alt="">
            <form class="form-signin" action="{% url 'account:login' %}" method="post">
                {% csrf_token %}
                {{ form.non_field_errors }}
                {% for field in form %}
                    {{ field }}
                    {{ field.errors }}
                {% endfor %}

                <input type="hidden" name="next" value="{{ redirect_to }}">
                <button class="btn btn-lg btn-primary btn-block" type="submit">Sign in</button>

                <div class="checkbox">
                    {% comment %}<a class="pull-right">Need help?</a>{% endcomment %}
                    <label>
                        <input type="checkbox" value="remember-me" name="remember"> Stay signed in
                    </label>
                </div>
                {% load oauth_tags %}
                {% load_oauth_applications request%}
            </form>
        </div>

        <p class="text-center">
            <a href="{% url "account:register" %}">
                {% trans 'Create Account' %}
            </a>
            |
            <a href="/">Home Page</a>
            |
            <a href="{% url "account:forget_password" %}">
                {% trans 'Forget Password' %}
            </a>
        </p>

    </div> <!-- /container -->
{% endblock %}