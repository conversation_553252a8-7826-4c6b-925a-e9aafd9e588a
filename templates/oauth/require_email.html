{% extends 'share_layout/base_account.html' %}

{% load static %}
{% block content %}
    <div class="container">

        <h2 class="form-signin-heading text-center">绑定您的邮箱账号</h2>

        <div class="card card-signin">
            {% if picture %}
                <img class="img-circle profile-img" src="{{ picture }}" alt="">
            {% else %}
                <img class="img-circle profile-img" src="{% static 'blog/img/avatar.png' %}" alt="">
            {% endif %}
            <form class="form-signin" action="" method="post">
                {% csrf_token %}
                {% comment %}<label for="inputEmail" class="sr-only">Email address</label>
                <input type="email" id="inputEmail" class="form-control" placeholder="Email" required autofocus>
                <label for="inputPassword" class="sr-only">Password</label>
                <input type="password" id="inputPassword" class="form-control" placeholder="Password" required>{% endcomment %}
                {{ form.non_field_errors }}
                {% for field in form %}
                    {{ field }}
                    {{ field.errors }}
                {% endfor %}


                <button class="btn btn-lg btn-primary btn-block" type="submit">提交</button>

                {% comment %}
                 <div class="checkbox">
                    <a class="pull-right">Need help?</a>
                    <label>
                        <input type="checkbox" value="remember-me"> Stay signed in
                    </label>
                </div>
{% endcomment %}
            </form>
        </div>

        <p class="text-center">
            <a href="{% url "account:login" %}">登录</a>
        </p>

    </div> <!-- /container -->
{% endblock %}