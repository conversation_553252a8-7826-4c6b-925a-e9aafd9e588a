<dev>
    <section id="comments" class="themeform">
        {% load blog_tags %}
        {% load comments_tags %}
        {% load cache %}

        <ul class="comment-tabs group">
            <li class="active"><a href="#commentlist-container"><i
                    class="fa fa-comments-o"></i>评论<span>{{ comment_count }}</span></a></li>

        </ul>
        {% if article_comments %}
            <div id="commentlist-container" class="comment-tab" style="display: block;">
                <ol class="commentlist">
                    {#                    {% query article_comments parent_comment=None as parent_comments %}#}
                    {% for comment_item in p_comments %}

                        {% with 0 as depth %}
                            {% include "comments/tags/comment_item_tree.html" %}
                        {% endwith %}
                    {% endfor %}

                </ol><!--/.commentlist-->
                <div class="navigation">
                    <nav class="nav-single">
                        {% if comment_prev_page_url %}
                            <div class="nav-previous">
                        <span><a href="{{ comment_prev_page_url }}" rel="prev"><span
                                class="meta-nav">←</span> 上一页</a></span>
                            </div>
                        {% endif %}
                        {% if comment_next_page_url %}
                            <div class="nav-next">
                        <span><a href="{{ comment_next_page_url }}" rel="next">下一页 <span
                                class="meta-nav">→</span></a></span>
                            </div>
                        {% endif %}
                    </nav>
                </div>
                <br/>
            </div>
        {% endif %}
    </section>

</dev>