# Generated by Django 4.2.5 on 2023-09-06 13:13

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bloguser',
            options={'get_latest_by': 'id', 'ordering': ['-id'], 'verbose_name': 'user', 'verbose_name_plural': 'user'},
        ),
        migrations.RemoveField(
            model_name='bloguser',
            name='created_time',
        ),
        migrations.RemoveField(
            model_name='bloguser',
            name='last_mod_time',
        ),
        migrations.AddField(
            model_name='bloguser',
            name='creation_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='creation time'),
        ),
        migrations.AddField(
            model_name='bloguser',
            name='last_modify_time',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='last modify time'),
        ),
        migrations.AlterField(
            model_name='bloguser',
            name='nickname',
            field=models.CharField(blank=True, max_length=100, verbose_name='nick name'),
        ),
        migrations.AlterField(
            model_name='bloguser',
            name='source',
            field=models.CharField(blank=True, max_length=100, verbose_name='create source'),
        ),
    ]
