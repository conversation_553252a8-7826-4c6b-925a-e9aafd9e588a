# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-13 16:02+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: .\accounts\admin.py:12
msgid "password"
msgstr "密碼"

#: .\accounts\admin.py:13
msgid "Enter password again"
msgstr "再次輸入密碼"

#: .\accounts\admin.py:24 .\accounts\forms.py:89
msgid "passwords do not match"
msgstr "密碼不匹配"

#: .\accounts\forms.py:36
msgid "email already exists"
msgstr "郵箱已存在"

#: .\accounts\forms.py:46 .\accounts\forms.py:50
msgid "New password"
msgstr "新密碼"

#: .\accounts\forms.py:60
msgid "Confirm password"
msgstr "確認密碼"

#: .\accounts\forms.py:70 .\accounts\forms.py:116
msgid "Email"
msgstr "郵箱"

#: .\accounts\forms.py:76 .\accounts\forms.py:80
msgid "Code"
msgstr "驗證碼"

#: .\accounts\forms.py:100 .\accounts\tests.py:194
msgid "email does not exist"
msgstr "郵箱不存在"

#: .\accounts\models.py:12 .\oauth\models.py:17
msgid "nick name"
msgstr "昵稱"

#: .\accounts\models.py:13 .\blog\models.py:29 .\blog\models.py:266
#: .\blog\models.py:284 .\comments\models.py:13 .\oauth\models.py:23
#: .\oauth\models.py:53
msgid "creation time"
msgstr "創建時間"

#: .\accounts\models.py:14 .\comments\models.py:14 .\oauth\models.py:24
#: .\oauth\models.py:54
msgid "last modify time"
msgstr "最後修改時間"

#: .\accounts\models.py:15
msgid "create source"
msgstr "來源"

#: .\accounts\models.py:33 .\djangoblog\logentryadmin.py:81
msgid "user"
msgstr "用戶"

#: .\accounts\tests.py:216 .\accounts\utils.py:39
msgid "Verification code error"
msgstr "驗證碼錯誤"

#: .\accounts\utils.py:13
msgid "Verify Email"
msgstr "驗證郵箱"

#: .\accounts\utils.py:21
#, python-format
msgid ""
"You are resetting the password, the verification code is：%(code)s, valid "
"within 5 minutes, please keep it properly"
msgstr "您正在重置密碼，驗證碼為：%(code)s，5分鐘內有效 請妥善保管."

#: .\blog\admin.py:13 .\blog\models.py:92 .\comments\models.py:17
#: .\oauth\models.py:12
msgid "author"
msgstr "作者"

#: .\blog\admin.py:53
msgid "Publish selected articles"
msgstr "發布選中的文章"

#: .\blog\admin.py:54
msgid "Draft selected articles"
msgstr "選中文章設為草稿"

#: .\blog\admin.py:55
msgid "Close article comments"
msgstr "關閉文章評論"

#: .\blog\admin.py:56
msgid "Open article comments"
msgstr "打開文章評論"

#: .\blog\admin.py:89 .\blog\models.py:101 .\blog\models.py:183
#: .\templates\blog\tags\sidebar.html:40
msgid "category"
msgstr "分類目錄"

#: .\blog\models.py:20 .\blog\models.py:179 .\templates\share_layout\nav.html:8
msgid "index"
msgstr "首頁"

#: .\blog\models.py:21
msgid "list"
msgstr "列表"

#: .\blog\models.py:22
msgid "post"
msgstr "文章"

#: .\blog\models.py:23
msgid "all"
msgstr "所有"

#: .\blog\models.py:24
msgid "slide"
msgstr "側邊欄"

#: .\blog\models.py:30 .\blog\models.py:267 .\blog\models.py:285
msgid "modify time"
msgstr "修改時間"

#: .\blog\models.py:63
msgid "Draft"
msgstr "草稿"

#: .\blog\models.py:64
msgid "Published"
msgstr "發布"

#: .\blog\models.py:67
msgid "Open"
msgstr "打開"

#: .\blog\models.py:68
msgid "Close"
msgstr "關閉"

#: .\blog\models.py:71 .\comments\admin.py:47
msgid "Article"
msgstr "文章"

#: .\blog\models.py:72
msgid "Page"
msgstr "頁面"

#: .\blog\models.py:74 .\blog\models.py:280
msgid "title"
msgstr "標題"

#: .\blog\models.py:75
msgid "body"
msgstr "內容"

#: .\blog\models.py:77
msgid "publish time"
msgstr "發布時間"

#: .\blog\models.py:79
msgid "status"
msgstr "狀態"

#: .\blog\models.py:84
msgid "comment status"
msgstr "評論狀態"

#: .\blog\models.py:88 .\oauth\models.py:43
msgid "type"
msgstr "類型"

#: .\blog\models.py:89
msgid "views"
msgstr "閱讀量"

#: .\blog\models.py:97 .\blog\models.py:258 .\blog\models.py:282
msgid "order"
msgstr "排序"

#: .\blog\models.py:98
msgid "show toc"
msgstr "顯示目錄"

#: .\blog\models.py:105 .\blog\models.py:249
msgid "tag"
msgstr "標簽"

#: .\blog\models.py:115 .\comments\models.py:21
msgid "article"
msgstr "文章"

#: .\blog\models.py:171
msgid "category name"
msgstr "分類名"

#: .\blog\models.py:174
msgid "parent category"
msgstr "上級分類"

#: .\blog\models.py:234
msgid "tag name"
msgstr "標簽名"

#: .\blog\models.py:256
msgid "link name"
msgstr "鏈接名"

#: .\blog\models.py:257 .\blog\models.py:271
msgid "link"
msgstr "鏈接"

#: .\blog\models.py:260
msgid "is show"
msgstr "是否顯示"

#: .\blog\models.py:262
msgid "show type"
msgstr "顯示類型"

#: .\blog\models.py:281
msgid "content"
msgstr "內容"

#: .\blog\models.py:283 .\oauth\models.py:52
msgid "is enable"
msgstr "是否啟用"

#: .\blog\models.py:289
msgid "sidebar"
msgstr "側邊欄"

#: .\blog\models.py:299
msgid "site name"
msgstr "站點名稱"

#: .\blog\models.py:305
msgid "site description"
msgstr "站點描述"

#: .\blog\models.py:311
msgid "site seo description"
msgstr "站點SEO描述"

#: .\blog\models.py:313
msgid "site keywords"
msgstr "關鍵字"

#: .\blog\models.py:318
msgid "article sub length"
msgstr "文章摘要長度"

#: .\blog\models.py:319
msgid "sidebar article count"
msgstr "側邊欄文章數目"

#: .\blog\models.py:320
msgid "sidebar comment count"
msgstr "側邊欄評論數目"

#: .\blog\models.py:321
msgid "article comment count"
msgstr "文章頁面默認顯示評論數目"

#: .\blog\models.py:322
msgid "show adsense"
msgstr "是否顯示廣告"

#: .\blog\models.py:324
msgid "adsense code"
msgstr "廣告內容"

#: .\blog\models.py:325
msgid "open site comment"
msgstr "公共頭部"

#: .\blog\models.py:352
msgid "Website configuration"
msgstr "網站配置"

#: .\blog\models.py:360
msgid "There can only be one configuration"
msgstr "只能有一個配置"

#: .\blog\views.py:348
msgid ""
"Sorry, the page you requested is not found, please click the home page to "
"see other?"
msgstr "抱歉，你所訪問的頁面找不到，請點擊首頁看看別的？"

#: .\blog\views.py:356
msgid "Sorry, the server is busy, please click the home page to see other?"
msgstr "抱歉，服務出錯了，請點擊首頁看看別的？"

#: .\blog\views.py:369
msgid "Sorry, you do not have permission to access this page?"
msgstr "抱歉，你沒用權限訪問此頁面。"

#: .\comments\admin.py:15
msgid "Disable comments"
msgstr "禁用評論"

#: .\comments\admin.py:16
msgid "Enable comments"
msgstr "啟用評論"

#: .\comments\admin.py:46
msgid "User"
msgstr "用戶"

#: .\comments\models.py:25
msgid "parent comment"
msgstr "上級評論"

#: .\comments\models.py:29
msgid "enable"
msgstr "啟用"

#: .\comments\models.py:34 .\templates\blog\tags\article_info.html:30
msgid "comment"
msgstr "評論"

#: .\comments\utils.py:13
msgid "Thanks for your comment"
msgstr "感謝你的評論"

#: .\comments\utils.py:15
#, python-format
msgid ""
"<p>Thank you very much for your comments on this site</p>\n"
"                    You can visit <a href=\"%(article_url)s\" rel=\"bookmark"
"\">%(article_title)s</a>\n"
"                    to review your comments,\n"
"                    Thank you again!\n"
"                    <br />\n"
"                    If the link above cannot be opened, please copy this "
"link to your browser.\n"
"                    %(article_url)s"
msgstr ""
"<p>非常感謝您對此網站的評論</p>\n"
" 您可以訪問<a href=\"%(article_url)s\" rel=\"書簽\">%(article_title)s</a>\n"
"查看您的評論，\n"
"再次感謝您！\n"
" <br />\n"
" 如果上面的鏈接打不開，請復製此鏈接鏈接到您的瀏覽器。\n"
"%(article_url)s"

#: .\comments\utils.py:26
#, python-format
msgid ""
"Your comment on <a href=\"%(article_url)s\" rel=\"bookmark\">"
"%(article_title)s</a><br/> has \n"
"                   received a reply. <br/> %(comment_body)s\n"
"                    <br/>   \n"
"                    go check it out!\n"
"                     <br/>\n"
"                     If the link above cannot be opened, please copy this "
"link to your browser.\n"
"                     %(article_url)s\n"
"                    "
msgstr ""
"您對 <a href=\"%(article_url)s\" rel=\"bookmark\">%(article_title)s</a><br/> "
"的評論有\n"
" 收到回復。<br/> %(comment_body)s\n"
"<br/>\n"
"快去看看吧！\n"
"<br/>\n"
" 如果上面的鏈接打不開，請復製此鏈接鏈接到您的瀏覽器。\n"
" %(article_url)s\n"
" "

#: .\djangoblog\logentryadmin.py:63
msgid "object"
msgstr "對象"

#: .\djangoblog\settings.py:140
msgid "English"
msgstr "英文"

#: .\djangoblog\settings.py:141
msgid "Simplified Chinese"
msgstr "簡體中文"

#: .\djangoblog\settings.py:142
msgid "Traditional Chinese"
msgstr "繁體中文"

#: .\oauth\models.py:30
msgid "oauth user"
msgstr "第三方用戶"

#: .\oauth\models.py:37
msgid "weibo"
msgstr "微博"

#: .\oauth\models.py:38
msgid "google"
msgstr "谷歌"

#: .\oauth\models.py:48
msgid "callback url"
msgstr "回調地址"

#: .\oauth\models.py:59
msgid "already exists"
msgstr "已經存在"

#: .\oauth\views.py:154
#, python-format
msgid ""
"\n"
"     <p>Congratulations, you have successfully bound your email address. You "
"can use\n"
"      %(oauthuser_type)s to directly log in to this website without a "
"password.</p>\n"
"       You are welcome to continue to follow this site, the address is\n"
"        <a href=\"%(site)s\" rel=\"bookmark\">%(site)s</a>\n"
"            Thank you again!\n"
"            <br />\n"
"        If the link above cannot be opened, please copy this link to your "
"browser.\n"
"        %(site)s\n"
"    "
msgstr ""
"\n"
"     <p>恭喜你已經綁定成功 你可以使用\n"
"      %(oauthuser_type)s 來免密登錄本站 </p>\n"
"       歡迎繼續關註本站, 地址是\n"
"        <a href=\"%(site)s\" rel=\"bookmark\">%(site)s</a>\n"
"            再次感謝你\n"
"            <br />\n"
"        如果上面鏈接無法打開，請復製此鏈接到你的瀏覽器 \n"
"        %(site)s\n"
"    "

#: .\oauth\views.py:165
msgid "Congratulations on your successful binding!"
msgstr "恭喜你綁定成功"

#: .\oauth\views.py:217
#, python-format
msgid ""
"\n"
"               <p>Please click the link below to bind your email</p>\n"
"\n"
"                 <a href=\"%(url)s\" rel=\"bookmark\">%(url)s</a>\n"
"\n"
"                 Thank you again!\n"
"                 <br />\n"
"                 If the link above cannot be opened, please copy this link "
"to your browser.\n"
"                  <br />\n"
"                 %(url)s\n"
"                "
msgstr ""
"\n"
" <p>請點擊下面的鏈接綁定您的郵箱</p>\n"
"\n"
" <a href=\"%(url)s\" rel=\"bookmark\">%(url)s</a>\n"
"\n"
"再次感謝您！\n"
" <br />\n"
"如果上面的鏈接打不開，請復製此鏈接到您的瀏覽器。\n"
"%(url)s\n"
" "

#: .\oauth\views.py:228 .\oauth\views.py:240
msgid "Bind your email"
msgstr "綁定郵箱"

#: .\oauth\views.py:242
msgid ""
"Congratulations, the binding is just one step away. Please log in to your "
"email to check the email to complete the binding. Thank you."
msgstr "恭喜您，還差一步就綁定成功了，請登錄您的郵箱查看郵件完成綁定，謝謝。"

#: .\oauth\views.py:245
msgid "Binding successful"
msgstr "綁定成功"

#: .\oauth\views.py:247
#, python-format
msgid ""
"Congratulations, you have successfully bound your email address. You can use "
"%(oauthuser_type)s to directly log in to this website without a password. "
"You are welcome to continue to follow this site."
msgstr ""
"恭喜您綁定成功，您以後可以使用%(oauthuser_type)s來直接免密碼登錄本站啦，感謝"
"您對本站對關註。"

#: .\templates\account\forget_password.html:7
msgid "forget the password"
msgstr "忘記密碼"

#: .\templates\account\forget_password.html:18
msgid "get verification code"
msgstr "獲取驗證碼"

#: .\templates\account\forget_password.html:19
msgid "submit"
msgstr "提交"

#: .\templates\account\login.html:36
msgid "Create Account"
msgstr "創建賬號"

#: .\templates\account\login.html:42
#, fuzzy
#| msgid "forget the password"
msgid "Forget Password"
msgstr "忘記密碼"

#: .\templates\account\result.html:18 .\templates\blog\tags\sidebar.html:126
msgid "login"
msgstr "登錄"

#: .\templates\account\result.html:22
msgid "back to the homepage"
msgstr "返回首頁吧"

#: .\templates\blog\article_archives.html:7
#: .\templates\blog\article_archives.html:24
msgid "article archive"
msgstr "文章歸檔"

#: .\templates\blog\article_archives.html:32
msgid "year"
msgstr "年"

#: .\templates\blog\article_archives.html:36
msgid "month"
msgstr "月"

#: .\templates\blog\tags\article_info.html:12
msgid "pin to top"
msgstr "置頂"

#: .\templates\blog\tags\article_info.html:28
msgid "comments"
msgstr "評論"

#: .\templates\blog\tags\article_info.html:58
msgid "toc"
msgstr "目錄"

#: .\templates\blog\tags\article_meta_info.html:6
msgid "posted in"
msgstr "發布於"

#: .\templates\blog\tags\article_meta_info.html:14
msgid "and tagged"
msgstr "並標記為"

#: .\templates\blog\tags\article_meta_info.html:25
msgid "by "
msgstr "由"

#: .\templates\blog\tags\article_meta_info.html:29
#, python-format
msgid ""
"\n"
"               title=\"View all articles published by "
"%(article.author.username)s\"\n"
"                    "
msgstr ""
"\n"
"               title=\"查看所有由 %(article.author.username)s\"發布的文章\n"
"                    "

#: .\templates\blog\tags\article_meta_info.html:44
msgid "on"
msgstr "在"

#: .\templates\blog\tags\article_meta_info.html:54
msgid "edit"
msgstr "編輯"

#: .\templates\blog\tags\article_pagination.html:4
msgid "article navigation"
msgstr "文章導航"

#: .\templates\blog\tags\article_pagination.html:9
msgid "earlier articles"
msgstr "早期文章"

#: .\templates\blog\tags\article_pagination.html:12
msgid "newer articles"
msgstr "較新文章"

#: .\templates\blog\tags\article_tag_list.html:5
msgid "tags"
msgstr "標簽"

#: .\templates\blog\tags\sidebar.html:7
msgid "search"
msgstr "搜索"

#: .\templates\blog\tags\sidebar.html:50
msgid "recent comments"
msgstr "近期評論"

#: .\templates\blog\tags\sidebar.html:57
msgid "published on"
msgstr "發表於"

#: .\templates\blog\tags\sidebar.html:65
msgid "recent articles"
msgstr "近期文章"

#: .\templates\blog\tags\sidebar.html:77
msgid "bookmark"
msgstr "書簽"

#: .\templates\blog\tags\sidebar.html:96
msgid "Tag Cloud"
msgstr "標簽雲"

#: .\templates\blog\tags\sidebar.html:107
msgid "Welcome to star or fork the source code of this site"
msgstr "歡迎您STAR或者FORK本站源代碼"

#: .\templates\blog\tags\sidebar.html:118
msgid "Function"
msgstr "功能"

#: .\templates\blog\tags\sidebar.html:120
msgid "management site"
msgstr "管理站點"

#: .\templates\blog\tags\sidebar.html:122
msgid "logout"
msgstr "登出"

#: .\templates\blog\tags\sidebar.html:129
msgid "Track record"
msgstr "運動軌跡記錄"

#: .\templates\blog\tags\sidebar.html:135
msgid "Click me to return to the top"
msgstr "點我返回頂部"

#: .\templates\oauth\oauth_applications.html:5
#| msgid "login"
msgid "quick login"
msgstr "快捷登錄"

#: .\templates\share_layout\nav.html:26
msgid "Article archive"
msgstr "文章歸檔"
