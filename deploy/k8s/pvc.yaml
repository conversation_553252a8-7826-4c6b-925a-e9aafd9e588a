apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: db-pvc
  namespace: djangoblog
spec:
  storageClassName: local-storage
  volumeName: local-pv-db
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi


---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: djangoblog-pvc
  namespace: djangoblog
spec:
  volumeName: local-pv-djangoblog
  storageClassName: local-storage
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: resource-pvc
  namespace: djangoblog
spec:
  volumeName: local-pv-resource
  storageClassName: local-storage
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: elasticsearch-pvc
  namespace: djangoblog
spec:
  volumeName: local-pv-elasticsearch
  storageClassName: local-storage
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  