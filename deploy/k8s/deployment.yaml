apiVersion: apps/v1
kind: Deployment
metadata:
  name: djangoblog
  namespace: djangoblog
  labels:
    app: djangoblog
spec:
  replicas: 3
  selector:
    matchLabels:
      app: djangoblog
  template:
    metadata:
      labels:
        app: djangoblog
    spec:
      containers:
      - name: djangoblog
        image: liangliangyy/djangoblog:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: djangoblog-env
        readinessProbe:
          httpGet:
            path: /
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 30
        livenessProbe:
          httpGet:
            path: /
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 30
        resources:
          requests:
            cpu: 10m
            memory: 100Mi
          limits:
            cpu: "2"
            memory: 2Gi
        volumeMounts:
        - name: djangoblog
          mountPath: /code/djangoblog/collectedstatic
        - name: resource
          mountPath: /resource
      volumes:
      - name: djangoblog
        persistentVolumeClaim:
          claimName: djangoblog-pvc
      - name: resource
        persistentVolumeClaim:
          claimName: resource-pvc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: djangoblog
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 6379
        resources:
          requests:
            cpu: 10m
            memory: 100Mi
          limits:
            cpu: 200m
            memory: 2Gi
       
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: db
  namespace: djangoblog
  labels:
    app: db
spec:
  replicas: 1
  selector:
    matchLabels:
      app: db
  template:
    metadata:
      labels:
        app: db
    spec:
      containers:
      - name: db
        image: mysql:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3306
        envFrom:
        - configMapRef:
            name: djangoblog-env
        readinessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - "-h"
            - "127.0.0.1"
            - "-u"
            - "root"
            - "-p$MYSQL_ROOT_PASSWORD"
          initialDelaySeconds: 10
          periodSeconds: 10
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - "-h"
            - "127.0.0.1"
            - "-u"
            - "root"
            - "-p$MYSQL_ROOT_PASSWORD"
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          requests:
            cpu: 10m
            memory: 100Mi
          limits:
            cpu: "2"
            memory: 2Gi
        volumeMounts:
        - name: db-data
          mountPath: /var/lib/mysql
      volumes:
      - name: db-data
        persistentVolumeClaim:
          claimName: db-pvc
      
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx
  namespace: djangoblog
  labels:
    app: nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 10m
            memory: 100Mi
          limits:
            cpu: "2"
            memory: 2Gi
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: nginx-config
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: djangoblog.conf
        - name: nginx-config
          mountPath: /etc/nginx/conf.d/resource.lylinux.net.conf
          subPath: resource.lylinux.net.conf
        - name: nginx-config
          mountPath: /etc/nginx/lylinux/resource.conf
          subPath: lylinux.resource.conf
        - name: djangoblog-pvc
          mountPath: /code/djangoblog/collectedstatic
        - name: resource-pvc
          mountPath: /resource
      volumes:
      - name: nginx-config
        configMap:
          name: web-nginx-config
      - name: djangoblog-pvc
        persistentVolumeClaim:
          claimName: djangoblog-pvc
      - name: resource-pvc
        persistentVolumeClaim:
          claimName: resource-pvc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
  namespace: djangoblog
  labels:
    app: elasticsearch
spec:
  replicas: 1
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: liangliangyy/elasticsearch-analysis-ik:8.6.1
        imagePullPolicy: IfNotPresent
        env:
        - name: discovery.type
          value: single-node
        - name: ES_JAVA_OPTS
          value: "-Xms256m -Xmx256m"
        - name: xpack.security.enabled
          value: "false"
        - name: xpack.monitoring.templates.enabled
          value: "false"
        ports:
        - containerPort: 9200
        resources:
          requests:
            cpu: 10m
            memory: 100Mi
          limits:
            cpu: "2"
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /
            port: 9200
          initialDelaySeconds: 15
          periodSeconds: 30
        livenessProbe:
          httpGet:
            path: /
            port: 9200
          initialDelaySeconds: 15
          periodSeconds: 30
        volumeMounts:
        - name: elasticsearch-data
          mountPath: /usr/share/elasticsearch/data/
      volumes:
        - name: elasticsearch-data
          persistentVolumeClaim:
            claimName: elasticsearch-pvc
