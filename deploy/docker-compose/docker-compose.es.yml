version: '3'

services:
  es:
    image: liangliangyy/elasticsearch-analysis-ik:8.6.1
    container_name: es
    restart: always
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - 9200:9200
    volumes:
      - ./bin/datas/es/:/usr/share/elasticsearch/data/

  kibana:
    image: kibana:8.6.1
    restart: always
    container_name: kibana
    ports:
      - 5601:5601
    environment:
      - ELASTICSEARCH_HOSTS=http://es:9200

  djangoblog:
    build: .
    restart: always
    command: bash -c 'sh /code/djangoblog/bin/docker_start.sh'
    ports:
      - "8000:8000"
    volumes:
      - ./collectedstatic:/code/djangoblog/collectedstatic
      - ./uploads:/code/djangoblog/uploads
    environment:
      - DJANGO_MYSQL_DATABASE=djangoblog
      - DJANGO_MYSQL_USER=root
      - DJANGO_MYSQL_PASSWORD=DjAnGoBlOg!2!Q@W#E
      - DJANGO_MYSQL_HOST=db
      - DJANGO_MYSQL_PORT=3306
      - <PERSON><PERSON><PERSON><PERSON>_MEMCACHED_LOCATION=memcached:11211
      - DJANGO_ELASTICSEARCH_HOST=es:9200
    links:
      - db
      - memcached
    depends_on:
      - db
    container_name: djangoblog

