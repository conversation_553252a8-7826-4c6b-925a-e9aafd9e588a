version: '3'

services:
  db:
    image: mysql:latest
    restart: always
    environment:
      - MYSQL_DATABASE=djangoblog
      - MYSQL_ROOT_PASSWORD=DjAnGoBlOg!2!Q@W#E
    ports:
      - 3306:3306
    volumes:
      - ./bin/datas/mysql/:/var/lib/mysql
    depends_on:
      - redis
    container_name: db

  djangoblog:
    build:
      context: ../../
    restart: always
    command: bash -c 'sh /code/djangoblog/bin/docker_start.sh'
    ports:
      - "8000:8000"
    volumes:
      - ./collectedstatic:/code/djangoblog/collectedstatic
      - ./logs:/code/djangoblog/logs
      - ./uploads:/code/djangoblog/uploads
    environment:
      - DJANGO_MYSQL_DATABASE=djangoblog
      - DJANGO_MYSQL_USER=root
      - DJANGO_MYSQL_PASSWORD=DjAnGoBlOg!2!Q@W#E
      - DJANGO_MYSQL_HOST=db
      - DJANG<PERSON>_MYSQL_PORT=3306
      - DJANGO_REDIS_URL=redis:6379
    links:
      - db
      - redis
    depends_on:
      - db
    container_name: djangoblog
  nginx:
    restart: always
    image: nginx:latest
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./bin/nginx.conf:/etc/nginx/nginx.conf
      - ./collectedstatic:/code/djangoblog/collectedstatic
    links:
      - djangoblog:djangoblog
    container_name: nginx

  redis:
    restart: always
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
