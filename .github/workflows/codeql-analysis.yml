name: "CodeQL"

on:
  push:
    branches:
      - master
      - dev
    paths-ignore:
      - '**/*.md'
      - '**/*.css'
      - '**/*.js'
      - '**/*.yml'
      - '**/*.txt'
  pull_request:
    branches:
      - master
      - dev
    paths-ignore:
      - '**/*.md'
      - '**/*.css'
      - '**/*.js'
      - '**/*.yml'
      - '**/*.txt'
  schedule:
    - cron: '30 1 * * 0'


jobs:
  CodeQL-Build:
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2

      - name: Autobuild
        uses: github/codeql-action/autobuild@v2

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2