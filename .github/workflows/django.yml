name: Django CI

on:
  push:
    branches:
      - master
      - dev
    paths-ignore:
      - '**/*.md'
      - '**/*.css'
      - '**/*.js'
  pull_request:
    branches:
      - master
      - dev
    paths-ignore:
      - '**/*.md'
      - '**/*.css'
      - '**/*.js'

jobs:
  build-normal:
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 4
      matrix:
        python-version: ["3.10","3.11" ]

    steps:
      - name: Start MySQL
        uses: samin/mysql-action@v1.3
        with:
          host port: 3306
          container port: 3306
          character set server: utf8mb4
          collation server: utf8mb4_general_ci
          mysql version: latest
          mysql root password: root
          mysql database: djangoblog
          mysql user: root
          mysql password: root

      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Run Tests
        env:
          DJANGO_MYSQL_PASSWORD: root
          DJANGO_MYSQL_HOST: 127.0.0.1
        run: |
          python manage.py makemigrations
          python manage.py migrate
          python manage.py test

  build-with-es:
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 4
      matrix:
        python-version: ["3.10","3.11" ]

    steps:
      - name: Start MySQL
        uses: samin/mysql-action@v1.3
        with:
          host port: 3306
          container port: 3306
          character set server: utf8mb4
          collation server: utf8mb4_general_ci
          mysql version: latest
          mysql root password: root
          mysql database: djangoblog
          mysql user: root
          mysql password: root

      - name: Configure sysctl limits
        run: |
          sudo swapoff -a
          sudo sysctl -w vm.swappiness=1
          sudo sysctl -w fs.file-max=262144
          sudo sysctl -w vm.max_map_count=262144

      - uses: miyataka/elasticsearch-github-actions@1

        with:
          stack-version: '7.12.1'
          plugins: 'https://release.infinilabs.com/analysis-ik/stable/elasticsearch-analysis-ik-7.12.1.zip'


      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Run Tests
        env:
          DJANGO_MYSQL_PASSWORD: root
          DJANGO_MYSQL_HOST: 127.0.0.1
          DJANGO_ELASTICSEARCH_HOST: 127.0.0.1:9200
        run: |
          python manage.py makemigrations
          python manage.py migrate
          coverage run manage.py test
          coverage xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v1

  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: .
          push: false
          tags: djangoblog/djangoblog:dev
